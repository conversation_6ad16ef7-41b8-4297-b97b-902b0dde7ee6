"""
预测器适配器
将现有的预测器适配到新的回测框架
"""

import pandas as pd
from typing import List, Tuple
from src.framework.interfaces import PredictorInterface
from src.framework.data_models import PredictionResult


class LotteryPredictorAdapter(PredictorInterface):
    """
    LotteryPredictor 适配器
    将现有的 LotteryPredictor 适配到新的回测框架接口
    """
    
    def __init__(self, original_predictor):
        """
        初始化适配器
        
        Args:
            original_predictor: 原始的 LotteryPredictor 实例
        """
        self.original_predictor = original_predictor
        self.predictor_name = "LotteryPredictor"
        self.predictor_version = "1.0.0"
    
    def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
        """
        为指定数据索引预测下一期
        
        Args:
            data_index: 数据索引（不是期号！）
            data: 完整的历史数据
            
        Returns:
            PredictionResult: 标准化的预测结果
        """
        # 更新预测器的数据（使用到当前索引的所有数据作为训练集）
        train_data = data.iloc[:data_index + 1].copy()
        self.original_predictor.data = train_data
        
        # 期号只作为标志
        period_number = str(data.iloc[data_index]['期号'])
        
        # 调用原始预测器的预测方法
        # 注意：这里传入的是相对于训练数据的索引（最后一期）
        prediction_dict = self.original_predictor.predict_next_period(0)
        
        # 转换为标准格式
        return self._convert_to_standard_format(prediction_dict, period_number, data_index)
    
    def _convert_to_standard_format(self, prediction_dict: dict, period_number: str, data_index: int) -> PredictionResult:
        """将原始预测结果转换为标准格式"""
        
        # 提取比例预测
        red_odd_even_predictions = []
        red_size_predictions = []
        blue_size_predictions = []
        
        if 'predictions' in prediction_dict:
            predictions = prediction_dict['predictions']
            
            # 红球奇偶比
            if 'red_odd_even' in predictions:
                red_odd_even_list = predictions['red_odd_even']
                red_odd_even_predictions = self._normalize_prediction_list(red_odd_even_list)

            # 红球大小比
            if 'red_size' in predictions:
                red_size_list = predictions['red_size']
                red_size_predictions = self._normalize_prediction_list(red_size_list)

            # 蓝球大小比
            if 'blue_size' in predictions:
                blue_size_list = predictions['blue_size']
                blue_size_predictions = self._normalize_prediction_list(blue_size_list)
        
        # 提取生成的号码
        generated_numbers = ([], [])
        if 'generated_numbers' in prediction_dict:
            generated_numbers = prediction_dict['generated_numbers']
        
        # 提取杀号信息
        kill_numbers = {}
        if 'kill_numbers' in prediction_dict:
            kill_numbers = prediction_dict['kill_numbers']
        
        # 提取贝叶斯选择
        bayes_selected = None
        if 'bayes_selected' in prediction_dict:
            bayes_selected = prediction_dict['bayes_selected']
        
        # 提取所有组合
        all_combinations = None
        if 'all_combinations' in prediction_dict:
            all_combinations = prediction_dict['all_combinations']
        
        return PredictionResult(
            period_number=period_number,
            data_index=data_index,
            red_odd_even_predictions=red_odd_even_predictions,
            red_size_predictions=red_size_predictions,
            blue_size_predictions=blue_size_predictions,
            generated_numbers=generated_numbers,
            kill_numbers=kill_numbers,
            bayes_selected=bayes_selected,
            all_combinations=all_combinations,
            predictor_name=self.predictor_name,
            training_data_size=len(self.original_predictor.data)
        )
    
    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return self.predictor_name
    
    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return self.predictor_version

    def _normalize_prediction_list(self, prediction_data) -> List[Tuple[str, float]]:
        """
        标准化预测数据格式，确保返回List[Tuple[str, float]]

        Args:
            prediction_data: 可能是单个元组或元组列表

        Returns:
            List[Tuple[str, float]]: 标准化的预测列表
        """
        if not prediction_data:
            return []

        # 如果是单个元组格式 ('3:2', 0.5)
        if isinstance(prediction_data, (list, tuple)) and len(prediction_data) == 2:
            if isinstance(prediction_data[0], str) and isinstance(prediction_data[1], (int, float)):
                # 这是单个预测结果，转换为列表格式
                return [prediction_data]

        # 如果已经是列表格式 [('3:2', 0.5), ('2:3', 0.3)]
        if isinstance(prediction_data, list):
            return [(pred, prob) for pred, prob in prediction_data]

        # 回退：尝试直接转换
        try:
            return [(pred, prob) for pred, prob in prediction_data]
        except:
            # 最后的回退：返回空列表
            return []


class AdvancedProbabilisticSystemAdapter(PredictorInterface):
    """
    AdvancedProbabilisticSystem 适配器
    """
    
    def __init__(self, original_system):
        """
        初始化适配器
        
        Args:
            original_system: 原始的 AdvancedProbabilisticSystem 实例
        """
        self.original_system = original_system
        self.predictor_name = "AdvancedProbabilisticSystem"
        self.predictor_version = "2.0.0"
    
    def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
        """为指定数据索引预测下一期"""
        period_number = str(data.iloc[data_index]['期号'])
        
        # 调用原始系统的预测方法
        kill_result = self.original_system.predict_kills_by_period(
            period_number=period_number,
            red_target_count=5,
            blue_target_count=1
        )
        
        # 转换为标准格式
        kill_numbers = {}
        generated_numbers = ([], [])  # 该系统主要做杀号，不生成号码
        
        if kill_result['success']:
            kill_numbers = {
                'red_universal': kill_result['red_kills'],
                'blue_universal': kill_result['blue_kills']
            }
        
        return PredictionResult(
            period_number=period_number,
            data_index=data_index,
            red_odd_even_predictions=[],  # 该系统不做比例预测
            red_size_predictions=[],
            blue_size_predictions=[],
            generated_numbers=generated_numbers,
            kill_numbers=kill_numbers,
            predictor_name=self.predictor_name,
            training_data_size=kill_result.get('train_data_periods', 0)
        )
    
    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return self.predictor_name
    
    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return self.predictor_version


class ImprovedPredictorAdapter(PredictorInterface):
    """
    ImprovedPredictor 适配器
    将 ImprovedPredictor 适配到新的回测框架接口
    """

    def __init__(self, original_predictor):
        """
        初始化适配器

        Args:
            original_predictor: 原始的 ImprovedPredictor 实例
        """
        self.original_predictor = original_predictor
        self.predictor_name = "ImprovedPredictor"
        self.predictor_version = "2.0.0"

    def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
        """
        为指定数据索引预测下一期

        Args:
            data_index: 数据索引（不是期号！）
            data: 完整的历史数据

        Returns:
            PredictionResult: 标准化的预测结果
        """
        # 更新预测器的数据（使用到当前索引的所有数据作为训练集）
        train_data = data.iloc[:data_index + 1].copy()
        self.original_predictor.data = train_data

        # 期号只作为标志
        period_number = str(data.iloc[data_index]['期号'])

        # 调用原始预测器的预测方法
        # 注意：predict_with_insights(0) 表示预测训练数据的下一期
        prediction_dict = self.original_predictor.predict_with_insights(0)

        # 转换为标准格式
        return self._convert_to_standard_format(prediction_dict, period_number, data_index)

    def _convert_to_standard_format(self, prediction_dict: dict, period_number: str, data_index: int) -> PredictionResult:
        """将原始预测结果转换为标准格式"""

        # 提取比例预测
        red_odd_even_predictions = []
        red_size_predictions = []
        blue_size_predictions = []

        if 'predictions' in prediction_dict:
            predictions = prediction_dict['predictions']

            # 红球奇偶比
            if 'red_odd_even' in predictions:
                red_odd_even_list = predictions['red_odd_even']
                red_odd_even_predictions = self._normalize_prediction_list(red_odd_even_list)

            # 红球大小比
            if 'red_size' in predictions:
                red_size_list = predictions['red_size']
                red_size_predictions = self._normalize_prediction_list(red_size_list)

            # 蓝球大小比
            if 'blue_size' in predictions:
                blue_size_list = predictions['blue_size']
                blue_size_predictions = self._normalize_prediction_list(blue_size_list)

        # 提取生成的号码
        generated_numbers = ([], [])
        if 'generated_numbers' in prediction_dict:
            generated_numbers = prediction_dict['generated_numbers']

        return PredictionResult(
            period_number=period_number,
            data_index=data_index,
            red_odd_even_predictions=red_odd_even_predictions,
            red_size_predictions=red_size_predictions,
            blue_size_predictions=blue_size_predictions,
            generated_numbers=generated_numbers,
            kill_numbers=prediction_dict.get('kill_numbers', {}),
            predictor_name=self.predictor_name,
            training_data_size=len(self.original_predictor.data)
        )

    def _normalize_prediction_list(self, prediction_list):
        """标准化预测列表格式"""
        if not prediction_list:
            return []

        normalized = []
        for item in prediction_list:
            if isinstance(item, (list, tuple)) and len(item) >= 2:
                # 格式：(状态, 概率)
                normalized.append((item[0], float(item[1])))
            elif isinstance(item, str):
                # 只有状态，默认概率
                normalized.append((item, 0.5))
            else:
                # 其他格式，尝试转换
                normalized.append((str(item), 0.5))

        return normalized

    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return self.predictor_name

    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return self.predictor_version


def create_predictor_adapter(predictor_type: str, original_predictor):
    """
    工厂方法：创建预测器适配器

    Args:
        predictor_type: 预测器类型 ('lottery', 'improved', 'advanced', 'ultimate')
        original_predictor: 原始预测器实例

    Returns:
        PredictorInterface: 适配后的预测器
    """
    if predictor_type == 'lottery':
        return LotteryPredictorAdapter(original_predictor)
    elif predictor_type == 'improved':
        return ImprovedPredictorAdapter(original_predictor)
    elif predictor_type == 'advanced':
        return AdvancedProbabilisticSystemAdapter(original_predictor)
    else:
        raise ValueError(f"不支持的预测器类型: {predictor_type}")


# 使用示例
def example_usage():
    """使用示例"""
    from src.systems.main import LotteryPredictor
    from src.framework import BacktestFramework, BacktestConfig
    
    # 创建原始预测器
    original_predictor = LotteryPredictor()
    
    # 创建适配器
    predictor_adapter = create_predictor_adapter('lottery', original_predictor)
    
    # 创建回测框架
    framework = BacktestFramework(original_predictor.data)
    
    # 配置回测
    config = BacktestConfig(
        num_periods=10,
        min_train_periods=50,
        display_periods=5
    )
    
    # 运行回测
    result = framework.run_backtest(predictor_adapter, config)
    
    # 显示结果
    print(f"回测完成：{result.get_success_rate():.1%} 成功率")
    print(f"2+1命中率：{result.statistics.hit_2_plus_1_rate:.1%}")
    
    return result
